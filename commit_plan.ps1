# 分类提交执行脚本
# 执行前请确保您在正确的Git仓库目录中

Write-Host "🎯 开始执行分类提交计划..." -ForegroundColor Green

# 第1步：提交基础配置文件
Write-Host "`n📝 第1步：提交.gitignore配置文件" -ForegroundColor Yellow
git add .gitignore
git commit -m "feat: Add .gitignore configuration

- Configure Git ignore patterns for C/embedded projects
- Exclude build artifacts and temporary files"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ .gitignore 提交成功" -ForegroundColor Green
} else {
    Write-Host "❌ .gitignore 提交失败" -ForegroundColor Red
    exit 1
}

# 第2步：提交开发工具配置
Write-Host "`n🔧 第2步：提交开发工具配置" -ForegroundColor Yellow
git add .C-expansion-pack/ .cursor/
git commit -m "feat: Add development environment configurations

- Add C expansion pack configurations for embedded development
- Add Cursor IDE rules and settings
- Configure development workflow tools"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 开发工具配置提交成功" -ForegroundColor Green
} else {
    Write-Host "❌ 开发工具配置提交失败" -ForegroundColor Red
    exit 1
}

# 第3步：提交构建脚本
Write-Host "`n🛠️ 第3步：提交构建脚本" -ForegroundColor Yellow
git add clean_build_files.ps1
git commit -m "feat: Add build management scripts

- Add PowerShell script for cleaning build artifacts
- Improve build process automation"

if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 构建脚本提交成功" -ForegroundColor Green
} else {
    Write-Host "❌ 构建脚本提交失败" -ForegroundColor Red
    exit 1
}

# 第4步：提交Single_mode项目
Write-Host "`n📡 第4步：提交Single_mode项目" -ForegroundColor Yellow

# 4a. 主机从机数据传输项目
Write-Host "  📤 4a. 提交主机从机数据传输项目..." -ForegroundColor Cyan
git add "Single_mode/16.主机与从机数据传输（主机串口-14AD-DOUBLE(1)/"
git commit -m "feat: Add Single_mode - Host-Slave data transmission project

- Add Nordic nRF52 host-slave communication implementation
- Include UART-based data transmission with 14AD-DOUBLE protocol
- Add complete SDK components and drivers
- Include BLE, ANT, and 802.15.4 protocol stacks"

if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ 主机从机数据传输项目提交成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ 主机从机数据传输项目提交失败" -ForegroundColor Red
    exit 1
}

# 4b. 蓝牙串口透传项目
Write-Host "  📶 4b. 提交蓝牙串口透传项目..." -ForegroundColor Cyan
git add "Single_mode/实验15：蓝牙串口透传-14AD-DOUBLE(1)/"
git commit -m "feat: Add Single_mode - Bluetooth UART transparent transmission

- Add Bluetooth Low Energy UART transparent transmission experiment
- Include complete BLE peripheral and central implementations
- Add Nordic SDK integration and examples"

if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ 蓝牙串口透传项目提交成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ 蓝牙串口透传项目提交失败" -ForegroundColor Red
    exit 1
}

# 第5步：提交dual_model项目
Write-Host "`n💓 第5步：提交dual_model项目" -ForegroundColor Yellow

# 5a. Heart模块
Write-Host "  ❤️ 5a. 提交Heart模块..." -ForegroundColor Cyan
git add dual_model/Heart/
git commit -m "feat: Add dual_model Heart module

- Add heart rate monitoring implementation
- Include nRF52832 specific configurations
- Add BLE heart rate service implementation
- Include complete Nordic SDK for nRF52832"

if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ Heart模块提交成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ Heart模块提交失败" -ForegroundColor Red
    exit 1
}

# 5b. Bowel模块
Write-Host "  🔬 5b. 提交Bowel模块..." -ForegroundColor Cyan
git add dual_model/Bowel/
git commit -m "feat: Add dual_model Bowel module

- Add bowel monitoring system implementation
- Include sensor integration and data processing
- Add wireless communication protocols"

if ($LASTEXITCODE -eq 0) {
    Write-Host "  ✅ Bowel模块提交成功" -ForegroundColor Green
} else {
    Write-Host "  ❌ Bowel模块提交失败" -ForegroundColor Red
    exit 1
}

# 完成
Write-Host "`n🎉 所有提交完成！" -ForegroundColor Green
Write-Host "📊 提交统计：" -ForegroundColor Yellow
git log --oneline | Measure-Object | ForEach-Object { Write-Host "  总提交数: $($_.Count)" -ForegroundColor Cyan }
Write-Host "`n📋 查看提交历史：" -ForegroundColor Yellow
git log --oneline --graph --decorate

Write-Host "`n✨ 分类提交计划执行完毕！" -ForegroundColor Green
