# C语言嵌入式项目编译中间文件清理脚本
# 用于清理Nordic SDK项目中的编译中间文件
# 注意：此脚本只删除编译生成的中间文件，不会影响源代码

param(
    [switch]$DryRun,  # 只显示将要删除的文件，不实际删除
    [switch]$Backup   # 创建备份（可选）
)

if ($DryRun) {
    Write-Host "=== 预览模式：只显示将要删除的文件，不实际删除 ===" -ForegroundColor Cyan
} else {
    Write-Host "开始清理C语言嵌入式项目编译中间文件..." -ForegroundColor Green
    Write-Host "注意：这只会删除编译生成的文件，源代码完全安全！" -ForegroundColor Yellow
}

# 定义要清理的文件扩展名
$extensions = @(
    "*.o", "*.obj",           # 目标文件
    "*.hex", "*.bin", "*.elf", # 可执行文件
    "*.map", "*.lst", "*.sym", # 调试信息文件
    "*.a", "*.lib",           # 库文件
    "*.d",                    # 依赖文件
    "*.su",                   # 栈使用文件
    "*.tmp", "*.temp"         # 临时文件
)

# 定义要清理的目录
$directories = @(
    "_build", "build", "Build", "BUILD",
    "Debug", "Release", "obj", "bin",
    ".vs", ".vscode/ipch"
)

$totalDeleted = 0

# 清理文件
foreach ($ext in $extensions) {
    Write-Host "清理文件类型: $ext" -ForegroundColor Yellow
    $files = Get-ChildItem -Path . -Recurse -Name $ext -ErrorAction SilentlyContinue
    foreach ($file in $files) {
        if ($DryRun) {
            Write-Host "  将删除: $file" -ForegroundColor Gray
            $totalDeleted++
        } else {
            try {
                Remove-Item $file -Force
                $totalDeleted++
                Write-Host "  删除: $file" -ForegroundColor Gray
            }
            catch {
                Write-Host "  无法删除: $file - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

# 清理目录
foreach ($dir in $directories) {
    Write-Host "清理目录: $dir" -ForegroundColor Yellow
    $dirs = Get-ChildItem -Path . -Recurse -Directory -Name $dir -ErrorAction SilentlyContinue
    foreach ($directory in $dirs) {
        if ($DryRun) {
            Write-Host "  将删除目录: $directory" -ForegroundColor Gray
            $totalDeleted++
        } else {
            try {
                Remove-Item $directory -Recurse -Force
                $totalDeleted++
                Write-Host "  删除目录: $directory" -ForegroundColor Gray
            }
            catch {
                Write-Host "  无法删除目录: $directory - $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

if ($DryRun) {
    Write-Host "预览完成！发现 $totalDeleted 个文件/目录可以清理" -ForegroundColor Green
    Write-Host "运行 '.\clean_build_files.ps1' (不带-DryRun参数) 来实际执行清理" -ForegroundColor Cyan
} else {
    Write-Host "清理完成！共删除 $totalDeleted 个文件/目录" -ForegroundColor Green
    Write-Host "建议运行 'git status' 检查清理效果" -ForegroundColor Cyan
}

Write-Host ""
Write-Host "=== 重要提醒 ===" -ForegroundColor Yellow
Write-Host "• 源代码文件(.c, .h)完全安全，未被触及" -ForegroundColor Green
Write-Host "• 项目配置文件保持不变" -ForegroundColor Green
Write-Host "• 下次编译时会自动重新生成这些文件" -ForegroundColor Green
Write-Host "• 如需恢复固件，重新编译即可" -ForegroundColor Green
